apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: postgresresources.db.seabury.app
spec:
  group: db.seabury.app
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              databaseName:
                type: string
                description: "Name of the database to create"
              username:
                type: string
                description: "Username for the database user"
              postgresInstance:
                type: object
                properties:
                  host:
                    type: string
                    default: "postgres-service"
                  port:
                    type: integer
                    default: 5432
                  adminSecretRef:
                    type: object
                    properties:
                      name:
                        type: string
                      namespace:
                        type: string
                    required: ["name"]
                  databaseName:
                    type: string
                    description: Login database for admin user
                required: ["adminSecretRef", "host", "databaseName"]
              secretName:
                type: string
                description: "Name of the secret to create with credentials"
              dropOnDelete:
                type: boolean
                default: false
                description: "Whether to drop database when resource is deleted"
            required: ["databaseName", "username", "postgresInstance"]
          status:
            type: object
            properties:
              phase:
                type: string
                enum: ["Pending", "Creating", "Ready", "Failed", "Deleting"]
                default: "Pending"
              message:
                type: string
              databaseCreated:
                type: boolean
                default: false
              userCreated:
                type: boolean
                default: false
              secretCreated:
                type: boolean
                default: false
              lastUpdated:
                type: string
                format: date-time
    subresources:
      status: {}
    additionalPrinterColumns:
    - name: Database
      type: string
      jsonPath: .spec.databaseName
    - name: User
      type: string
      jsonPath: .spec.username
    - name: Status
      type: string
      jsonPath: .status.phase
    - name: Age
      type: date
      jsonPath: .metadata.creationTimestamp
  scope: Namespaced
  names:
    plural: postgresresources
    singular: postgresresource
    kind: PostgresResource
    shortNames:
    - pgres
    - pg
