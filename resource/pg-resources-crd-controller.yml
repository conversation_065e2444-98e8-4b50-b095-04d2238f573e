apiVersion: v1
kind: Namespace
metadata:
  name: postgres-operator
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: postgres-controller
  namespace: postgres-operator
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: postgres-controller
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["db.seabury.app"]
  resources: ["postgresresources"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["db.seabury.app"]
  resources: ["postgresresources/status"]
  verbs: ["get", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: postgres-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: postgres-controller
subjects:
- kind: ServiceAccount
  name: postgres-controller
  namespace: postgres-operator
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-controller
  namespace: postgres-operator
  labels:
    app: postgres-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-controller
  template:
    metadata:
      labels:
        app: postgres-controller
    spec:
      serviceAccountName: postgres-controller
      imagePullSecrets:
        - name: ghcr-pg-resource-secret
      containers:
      - name: controller
        image: ghcr.io/rmcc13/pg-resource-controller:v0.1.1
        resources:
          requests:
            memory: 128Mi
            cpu: 100m
          limits:
            memory: 256Mi
            cpu: 200m
