FROM node:22-alpine AS builder

WORKDIR /usr/src/app

COPY package.json yarn.lock .yarnrc.yml tsconfig.json ./
RUN corepack enable && yarn install

COPY src src
RUN yarn build

FROM node:22-alpine

WORKDIR /app
COPY --from=builder /usr/src/app/node_modules node_modules
COPY --from=builder /usr/src/app/dist dist
COPY --from=builder /usr/src/app/package.json package.json
COPY README.md README.md

ENTRYPOINT [ "node", "dist/index.js" ]
