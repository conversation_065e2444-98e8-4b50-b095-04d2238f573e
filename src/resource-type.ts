export type PgResourceSpec = {
  /**
   * Name of the database the requesting app wants.
   */
  databaseName: string;

  /**
   * Login username the requesting app wants.
   */
  username: string;

  /**
   * Privileges that {@see username} has on {@see databasename}.
   * defaults to ["ALL"]
   */
  // TODO: Add support for customizing privileges
  // privileges: string[];

  /**
   * Name of the secret to create with the new login credentials
   */
  secretName: string;

  /**
   * If true, drops the database when the resource is deleted.
   * Defaults to false
   */
  dropOnDelete: boolean;

  /**
   * Database server details
   */
  postgresInstance: {
    /**
     * Server hostname.
     */
    host: string;
    /**
     * Server port. Defaults to 5432
     */
    port?: number

    /**
     * Admin login database.
     * Defaults to postgres
     */
    databaseName: string;

    /**
     * Secret that contains admin login credentials to the server.
     */
    adminSecretRef: {
      /**
       * Name of the secret that contains the admin credentials.
       */
      name: string;
      /**
       * Namespace the secret resides in.
       */
      namespace?: string;
    }
  }
}

export type PgResource = {
  metadata: {
    name: string;
    namespace: string;
  };
  spec: PgResourceSpec;
}

export type PgStatus = {
  phase: 'Pending' | 'Creating' | 'Ready' | 'Failed' | 'Deleting'
  message: string;
  databaseCreated: boolean;
  userCreated: boolean;
  secretCreated: boolean;
  lastUpdated: Date;
}
