import * as k8s from '@kubernetes/client-node';
import { PgResource } from './resource-type.js';

const GROUP = 'db.seabury.app';
const VERSION = 'v1';
const PLURAL = 'postgresresources';

const getApis = async () => {
  const kc = new k8s.KubeConfig();
  if (process.env['KUBERNETES_SERVICE_HOST']) {
    kc.loadFromCluster();
  } else {
    kc.loadFromDefault();
  }

  const k8sApi = kc.makeApiClient(k8s.CoreV1Api);
  const customObjectsApi = kc.makeApiClient(k8s.CustomObjectsApi);

  return {
    kc,
    k8sApi,
    customObjectsApi
  }
}

const main = async() => {
  console.log('controller starting  up');
  let res: Awaited<ReturnType<typeof getApis>>;
  try {
    res = await getApis();
  } catch (e: any) {
    console.log('Failed to load APIs: ', e.message || e.toString());
    throw e;
  }

  const { kc, customObjectsApi } = res;
  const watch = new k8s.Watch(kc);

  const startWatch = async () => {
    await watch.watch(
      `/apis/${GROUP}/${VERSION}/${PLURAL}`,
      { allowWatchBookmarks: true },
      async (type, obj: PgResource) => {
        if (type === 'ADDED' || type === 'MODIFIED') {
          console.log('Adding or modifying type');
          await customObjectsApi.patchNamespacedCustomObjectStatus({
            group: GROUP,
            plural: PLURAL,
            version: VERSION,
            name: obj.metadata.name,
            namespace: obj.metadata.namespace,
            body: {
              status: {
                phase: 'Ready',
                message: 'Resource created',
                databaseCreated: false,
                userCreated: false,
                secretCreated: false,
                lastUpdated: new Date()
              }
            }
          });
        } else if (type === 'DELETED') {
          console.log('Deleting type');
           await customObjectsApi.patchNamespacedCustomObjectStatus({
            group: GROUP,
            plural: PLURAL,
            version: VERSION,
            name: obj.metadata.name,
            namespace: obj.metadata.namespace,
            body: {
              status: {
                phase: 'Deleting',
                message: 'Resource being deleted',
                databaseCreated: false,
                userCreated: false,
                secretCreated: false,
                lastUpdated: new Date()
              }
            }
          });
        }


        console.log(JSON.stringify(obj));

      },
      (error: any) => {
        console.error('Watch error: ', error.message);
        console.log('Restarting watch in 5 seconds...');
        setTimeout(startWatch, 5000);
      }
    )
  }

  await startWatch();
};

process.on('SIGINT', () => {
  console.log('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

main().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
